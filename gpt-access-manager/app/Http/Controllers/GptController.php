<?php

namespace App\Http\Controllers;

use App\Models\Gpt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class GptController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $gpts = Auth::user()->gpts;
        return view('gpts.index', compact('gpts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('gpts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        Auth::user()->gpts()->create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
        ]);

        return redirect()->route('gpts.index')->with('success', 'GPT created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }
        return view('gpts.edit', compact('gpt'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $gpt->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('gpts.index')->with('success', 'GPT updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gpt $gpt)
    {
        if ($gpt->user_id !== Auth::id()) {
            abort(403);
        }

        $gpt->delete();

        return redirect()->route('gpts.index')->with('success', 'GPT deleted successfully.');
    }
}

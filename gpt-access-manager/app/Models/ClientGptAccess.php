<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClientGptAccess extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'gpt_id',
        'token',
        'status',
        'expired_at',
        'usage_limit',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function gpt(): BelongsTo
    {
        return $this->belongsTo(Gpt::class);
    }
}
